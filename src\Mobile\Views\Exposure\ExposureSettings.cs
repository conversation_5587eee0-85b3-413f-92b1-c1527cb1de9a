﻿namespace AppoMobi.Main
{
    /// <summary>
    /// Container for exposure settings
    /// </summary>
    public class ExposureSettings
    {
        public double Aperture { get; set; }
        public double ShutterSpeed { get; set; }
        public double ISO { get; set; }

        public ExposureSettings(double aperture, double shutterSpeed, double iso)
        {
            Aperture = aperture;
            ShutterSpeed = shutterSpeed;
            ISO = iso;
        }

        public double GetEV() => ExposureCalculator.CalculateEV(Aperture, ShutterSpeed, ISO);

        public override string ToString()
        {
            return $"ISO{ISO:F0} (EV: {GetEV():F1}) • f/{Aperture:F1} • {(ShutterSpeed < 1 ? $"1/{(1 / ShutterSpeed):F0}" : $"{ShutterSpeed:F1}s")}";
        }
    }
}
