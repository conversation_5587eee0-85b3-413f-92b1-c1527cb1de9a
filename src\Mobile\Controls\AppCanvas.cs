﻿using DrawnUi.Views;

namespace AppoMobi.Main
{
    public class AppCanvas : Canvas
    {
        public AppCanvas(SkiaControl content)
        {
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;
            Gestures = GesturesMode.Lock;
            RenderingMode = RenderingModeType.Accelerated;

            Content = new SkiaLayer()
            {
                VerticalOptions = LayoutOptions.Fill,
                Children = new List<SkiaControl>()
                {
                    content,
#if DEBUG
                    new SkiaLabelFps()
                    {
                        Margin = new(0, 0, 4, 28),
                        BackgroundColor = Colors.DarkRed,
                        ForceRefresh = false,
                        HorizontalOptions = LayoutOptions.End,
                        Rotation = -45,
                        TextColor = Colors.White,
                        VerticalOptions = LayoutOptions.End
                    }
#endif
                }
            };
        }
    }
}
