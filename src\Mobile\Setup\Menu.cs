﻿using AppoMobi.Forms.Common.ResX;
using AppoMobi.Forms.Content.Camera;
using AppoMobi.Forms.Content.Camera.Models;
using AppoMobi.Forms.Controls.PickerControl;
using AppoMobi.Forms.Models;
using AppoMobi.Main;
using AppoMobi.Models;
using AppoMobi.Pages;
using AppoMobi.Xam;
using System.Collections.Generic;
using System.Linq;


namespace AppoMobi //appo
{
    public static class TabsAndMenu
    {
        public const int MaxTabs = 5;

        public static List<MenuPageContentItem> MenuListAuthorized = new List<MenuPageContentItem>();
        public static List<MenuPageContentItem> MenuListUnauthorized = new List<MenuPageContentItem>();
        public static List<MenuPageContentItem> MenuListDebug = new List<MenuPageContentItem>();

        public static List<MenuPageContentItem> MenuList = new List<MenuPageContentItem>();
        public static List<TabbedMenuItem> TabsList = new List<TabbedMenuItem>();
        public static List<string> ModulesList = new List<string>();


 
        public static void Init()

        {
            MenuList.Clear();
            MenuListAuthorized.Clear();
            MenuListUnauthorized.Clear();
            MenuListDebug.Clear();
            TabsList.Clear();

            TabsList.AddRange(new[]
            {
                //NEGATIVE CAM
                new TabbedMenuItem
                {
                    //Platform = Device.iOS,
                    Id = "cam",
                    NameInTabs = ResStrings.Camera,
                    NameInHeader = ResStrings.CameraFull,
                    IconString = FaPro.ImagePolaroid,
                    ContentClass = typeof(ContentCameraNegative),
                    ContentIsFullScreen = true,
                    HiddenDefault = true,
                    //Platform = "iOS Android"
                },

                //VIEWPORT CAM
                new TabbedMenuItem
                {
                    Id = "camframes",
                    //Platform = Device.iOS,
                    NameInTabs = ResStrings.Viewfinder,
                    NameInHeader = ResStrings.ViewfinderFull,
                    IconString = FaPro.ExpandWide,
                    ContentClass = typeof(ContentCameraZoom),
                    ContentIsFullScreen = true,
                    HiddenDefault = true,
                    //Platform = "iOS Android"
                },
                new TabbedMenuItem
                {
                    Id = "expo",
                    NameInTabs = ResStrings.SpotMeterShort,
                    NameInHeader = ResStrings.SpotMeter,
                    IconString = FaPro.Aperture,
                    ContentClass = typeof(ContentCameraExposure),
                    ContentIsFullScreen = true,
                    HiddenDefault = true,
                },
                new TabbedMenuItem
                {
                    Id = "calc",
                    ContentIsFullScreen = true,
                    NameInTabs = ResStrings.X_TimeCalcShort,
                    NameInHeader = ResStrings.X_TimeCalcFull,
                    IconString = FaPro.CalculatorSimple,
                    IconSourceAjustScale = 0.95,
                    ContentClass = typeof(ContentTimeCalculator),
                    //ContentClass = typeof(ContentTimeCalculator),
                },
                new TabbedMenuItem
                {
                    Id = "reciprocity",
                    NameInTabs = ResStrings.X_Reciprocity,
                    NameInHeader = ResStrings.X_Reciprocity,
                    IconString = FaPro.SunBright,
                    ContentClass = typeof(ContentReciprocity),
                    ContentIsFullScreen = true,
                },
                new TabbedMenuItem
                {
                    Id = "bellows",
                    NameInTabs = ResStrings.X_BellowsShort,
                    NameInHeader = ResStrings.X_BellowsFull,
                    IconString = FaPro.CameraSecurity,
                    ContentClass = typeof(ContentMeh),
                },
                new TabbedMenuItem
                {
                    Id = "35mm",
                    NameInTabs = ResStrings.X_35MmShort,
                    NameInHeader = ResStrings.X_35mmFull,
                    IconString = FaPro.Film,
                    IconSourceAjustScale = 1.1,
                    ContentClass = typeof(Content35mm),
                    HiddenDefault = true
                },
                new TabbedMenuItem
                {
                    Id = "developer",
                    NameInTabs = ResStrings.X_DeveloperShort,
                    NameInHeader = ResStrings.X_DeveloperFull,
                    IconString = FaPro.Fill,
                    ContentClass = typeof(ContentDevelop),
                },
                new TabbedMenuItem
                {
                    Id = "light",
                    NameInTabs = ResStrings.LightPadShort,
                    NameInHeader = ResStrings.LightPad,
                    IconString = FaPro.Lightbulb,
                    ContentClass = typeof(ContentLightPad),
                    ContentIsFullScreen = true,
                    HiddenDefault = true,
                },

                /*
                new TabbedMenuItem
                {
                    NameInTabs = ResStrings.Settings,
                    NameInHeader=ResStrings.Settings,
                    IconString =  "\uf509",
                    PageClass = typeof(PageSettings),
                    ZoomIcon = true
                },
                */

                //new TabbedMenuItem //preferences
                //{
                //    NameInTabs = ResStrings.iOSTabsStartup_Setup_Favorites.ToUpper(),
                //    NameInHeader=ResStrings.iOSTabsStartup_Setup_Favorites,
                //    Icon = "heart", PageClass = typeof(PagePlaceholder),// PageUserFavs
                //    Module = "" //any
                //},
            });


            MenuListAuthorized.AddRange(new[]
            {
                new MenuPageContentItem
                {
                    Id = "logoff",
                    IconString = "\uf2f5", //f08b
                    //IconSource =  "resource://AppoMobi.Mobile.Images.Icons.exit.png",
                    NameInTabs = ResStrings.Exit
                },
            });


            MenuListUnauthorized.AddRange(new[]
            {
//#if DEBUG
//                new MenuPageContentItem
//				{   OnSelected= () =>
//					{
//                        //var popup = new TestDebug();
//                        //popup.Show();

//                        var lenses = new List<FocalLengthItem>
//						{
//							new FocalLengthItem
//							{
//								Title = "35mm",
//								Id="35"
//							},
//							new FocalLengthItem
//							{
//								Title = "100mm",
//								Id="100"
//							},
//						};

//                        //output list
//                        var buidList = new List<OptionItem>();
//						foreach (var item in lenses)
//						{
//							buidList.Add(new OptionItem
//							{
//								Title = item.Title, Id = item.Id
//							});
//						}

//                        var dialog = new EditableListView(null, "Объективы для  \"35mm\"", buidList);
//                        dialog.Open();

//                        return;


//						List<OptionItem> multiselectionList = MultiPicker.BuildMultiselectionList(
//							lenses,
//							(item) =>
//							{
//								return false;
//							}
//						).ToList();

//						var onPickerSubmit = new Command(async (object context) =>
//						{
//							foreach (var item in multiselectionList.Where(x => x.Selected))
//							{
//                                //todo
//                                var stop=true;
//							}
//						});

//						MultiPicker.PresentForMultiOptions(multiselectionList, onPickerSubmit, 1, 5, "DEBUG");

//					},
//					IconString =FaPro.Sparkles,
//					Title ="DEBUG",},
//#endif

                new MenuPageContentItem
                {
                    Id = "news",
                    //Modal = true,
                    IconString = FaPro.Sparkles,
                    //PageClass = typeof(ViewWebNews),
                    ContentClass = typeof(ContentNews),
                    NameInTabs = ResStrings.X_OurNews,
                    NameInHeader = ResStrings.X_OurNews,
                },

                new MenuPageContentItem
                {
                    Id = "contacts",
                    //Modal = true,
                    //IconString = "\uf05a",
                    IconSource = App.Current.Resources.Get<string>("SvgLogoPlain"),
                    ContentClass = typeof(ContentAboutUsDrawn),
                    NameInTabs = ResStrings.X_AboutUs,
                    NameInHeader = ResStrings.X_AboutUs,
                },

                //new MenuPageItem
                //{Key = "camera",
                //    Platform = Device.iOS,
                //    Modal = true,
                //    IconString = FaPro.CameraAlt,
                //    //ContentType = typeof(ContentCameraNegative),
                //    TargetType = typeof(PageCamera),
                //    Title =ResStrings.Camera,},

                new MenuPageContentItem
                {
                    Id = "settings",
                    //Modal = true,
                    NameInHeader = ResStrings.Settings,
                    NameInTabs =  ResStrings.Settings,
                    IconString = FaPro.Gear,
                    ContentClass = typeof(ContentSettings),
                },
            });

            //hoho hehe =))
            //AddTabsToList(ref MenuListUnauthorized, true);


            MenuList.AddRange(MenuListUnauthorized);


#if DEBUG
            //  MenuList.AddRange(MenuListDebug);
#endif
        }


        static TabsAndMenu()

        {
            ModulesList.AddRange(new[] //todo read from remote?..
            {
                "main", "push",
            });

            // ************ MENU ***************
        }
    }
}
