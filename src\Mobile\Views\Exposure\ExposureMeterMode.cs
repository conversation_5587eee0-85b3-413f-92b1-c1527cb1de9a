﻿namespace AppoMobi.Main
{
    /// <summary>
    /// Professional exposure meter modes similar to camera exposure modes
    /// </summary>
    public enum ExposureMeterMode
    {
        /// <summary>
        /// Lock aperture, adjust shutter + ISO
        /// </summary>
        AperturePriority,

        /// <summary>
        /// Lock shutter, adjust aperture + ISO  
        /// </summary>
        ShutterPriority,

        /// <summary>
        ///  Lock ISO, adjust aperture + shutter (most common)
        /// </summary>
        ISOPriority,   

        /// <summary>
        /// All unlocked, prefer shutter adjustment
        /// </summary>
        Manual
    }
}
