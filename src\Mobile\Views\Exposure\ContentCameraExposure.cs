﻿using System.Diagnostics;
using AppoMobi.Main;

namespace AppoMobi.Main
{
    public class ContentCameraExposure : ScreenCanvas
    {
        public ContentCameraExposure()
        {
            Create();
        }

        public ContentCameraExposure(IPageEnhancedNav daddy)
        {
            Create();

            Daddy = daddy;
        }

        public override void OnAppearing()
        {
            Debug.WriteLine($"OnAppearing {this}");

            WithCamera?.OnAppearing();

            base.OnAppearing();
        }

        public override void OnDisappearing()
        {
            Debug.WriteLine($"OnDisappearing {this}");

            base.OnDisappearing();

            WithCamera?.OnDisappearing();

            GC.Collect();
        }

        private ExposureMeter? WithCamera;

        //public override void OnTabDeactivated()
        //{
        //    Debug.WriteLine($"OnTabDeactivated {this}");
        //    base.OnTabDeactivated();

        //    GC.Collect();
        //}

        //public override void OnTabActivated()
        //{
        //    Debug.WriteLine($"OnTabActivated {this}");
        //    base.OnTabActivated();
        //}

        void Create()
        {
            RenderingMode = RenderingModeType.Accelerated;
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;

            BackgroundColor = Colors.Black;

            WithCamera = new ExposureMeter() { };

            Content = WithCamera;
        }
    }
}
