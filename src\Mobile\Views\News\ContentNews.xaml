﻿<?xml version="1.0" encoding="utf-8"?>

<pages:IncludedContent
    x:Class="AppoMobi.Main.ContentNews"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pages="clr-namespace:AppoMobi.Pages"
    xmlns:ui="clr-namespace:AppoMobi.UI"
    xmlns:appoMobi="clr-namespace:AppoMobi"
    xmlns:xam="clr-namespace:AppoMobi.Xam"
    x:Name="MainScroll"
    Padding="8,0,8,8"
    VerticalOptions="FillAndExpand">

    <Grid HorizontalOptions="FillAndExpand" VerticalOptions="FillAndExpand">

        <!--
                    CustomNavigated="MyBrowser_OnNavigated"
                    CustomNavigating="MyBrowser_OnNavigating"
                -->

        <xam:XamWebView
            x:Name="ControlBrowser"
            Clicked="OnClicked"
            HorizontalOptions="Fill"
            Navigated="MyBrowser_OnNavigated"
            Navigating="MyBrowser_OnNavigating"
            SizeChanged="ControlBrowser_OnSizeChanged"
            VerticalOptions="Fill"
            WebViewColor="White" />


        <!--<BoxView
                    x:Name="ControlHide"
                    BackgroundColor="{Binding Source={x:Reference ControlBrowser}, Path=WebViewColor}"
                    HorizontalOptions="FillAndExpand"
                    VerticalOptions="FillAndExpand" />-->

    </Grid>


</pages:IncludedContent>